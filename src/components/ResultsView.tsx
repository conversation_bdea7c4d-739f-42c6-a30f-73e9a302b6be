import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { downloadSRT } from '@/lib/srt';
import { Download, FileText } from 'lucide-react';

interface ResultsViewProps {
  clips: any[];
  onReset: () => void;
}

export default function ResultsView({ clips, onReset }: ResultsViewProps) {
  const handleDownload = (clip: any, index: number) => {
    const link = document.createElement('a');
    link.href = clip.url;
    link.download = `clip_${index + 1}.mp4`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleDownloadSRT = (clip: any, index: number) => {
    if (clip.words && clip.words.length > 0) {
      downloadSRT(clip.words, `clip_${index + 1}_subtitles`);
    } else {
      console.warn('No subtitle data available for this clip');
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-green-400 mb-2">🎉 Your Clips Are Ready!</h2>
        <p className="text-slate-400">
          We've identified {clips.length} engaging moments from your video.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {clips.map((clip, index) => (
          <Card key={index} className="p-4">
            <div className="space-y-4">
              <video
                src={clip.url}
                controls
                className="w-full rounded-md"
                preload="metadata"
              >
                Your browser does not support the video tag.
              </video>
              
              <div className="space-y-2">
                <p className="text-sm text-slate-400">
                  Clip {index + 1} • {Math.round(clip.end - clip.start)}s
                </p>
                <p className="text-sm bg-slate-800 p-3 rounded-md">
                  "{clip.transcript}"
                </p>
              </div>
              
              <div className="flex gap-2">
                <Button
                  onClick={() => handleDownload(clip, index)}
                  variant="outline"
                  className="flex-1"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download Video
                </Button>

                <Button
                  onClick={() => handleDownloadSRT(clip, index)}
                  variant="outline"
                  className="flex-1"
                  disabled={!clip.words || clip.words.length === 0}
                >
                  <FileText className="w-4 h-4 mr-2" />
                  Download .SRT
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>

      <div className="text-center">
        <Button onClick={onReset} size="lg">
          Process Another Video
        </Button>
      </div>
    </div>
  );
}
