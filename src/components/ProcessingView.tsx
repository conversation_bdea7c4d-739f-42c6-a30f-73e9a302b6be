import { FFmpeg } from '@ffmpeg/ffmpeg';
import VideoPreview from '@/components/VideoPreview';
import StatusPanel from '@/components/StatusPanel';
import ResultsView from '@/components/ResultsView';

interface ProcessingViewProps {
  videoFile: File | null;
  appState: string;
  processingStep: string;
  resultClips: any[];
  onStartProcessing: () => Promise<void>;
  onReset: () => void;
  ffmpegRef: React.RefObject<FFmpeg | null>;
  onDebugAudio?: () => Promise<void>;
  progress: number;
  errorMessage: string | null;
  ffmpegLoaded: boolean;
}

export default function ProcessingView({
  videoFile,
  appState,
  processingStep,
  resultClips,
  onStartProcessing,
  onReset,
  ffmpegRef,
  onDebugAudio,
  progress,
  errorMessage,
  ffmpegLoaded
}: ProcessingViewProps) {

  if (appState === 'error') {
    return (
      <div className="space-y-6">
        <VideoPreview videoFile={videoFile} />
        <div className="bg-red-50 border border-red-200 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <div className="text-red-500">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-red-800">Processing Error</h3>
              <p className="text-red-700 mt-1">{errorMessage}</p>
            </div>
          </div>
          <button
            onClick={onReset}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            Process Another Video
          </button>
        </div>
      </div>
    );
  }

  if (processingStep === 'done') {
    return <ResultsView clips={resultClips} onReset={onReset} />;
  }

  return (
    <div className="flex flex-col md:flex-row gap-6">
      {/* Left Column - Video Preview */}
      <div className="flex-grow md:w-2/3">
        <VideoPreview videoFile={videoFile} />
      </div>

      {/* Right Column - Status Panel */}
      <div className="md:w-1/3">
        <StatusPanel
          appState={appState}
          processingStep={processingStep}
          onStart={onStartProcessing}
          ffmpegRef={ffmpegRef}
          onDebugAudio={onDebugAudio}
          progress={progress}
          ffmpegLoaded={ffmpegLoaded}
        />
      </div>
    </div>
  );
}
