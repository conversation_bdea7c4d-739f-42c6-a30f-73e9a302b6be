// src/lib/srt.ts

interface Word {
  text: string;
  start: number; // milliseconds
  end: number;   // milliseconds
}

/**
 * Converts milliseconds to SRT time format (HH:MM:SS,mmm)
 * @param milliseconds - Time in milliseconds
 * @returns Formatted time string for SRT
 */
const formatTime = (milliseconds: number): string => {
  const totalSeconds = Math.floor(milliseconds / 1000);
  const ms = milliseconds % 1000;
  
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`;
};

/**
 * Groups words into subtitle segments based on timing and length constraints
 * @param words - Array of word objects with timing information
 * @param maxSegmentLength - Maximum characters per subtitle line
 * @param maxDuration - Maximum duration for a subtitle segment in milliseconds
 * @returns Array of subtitle segments
 */
const groupWordsIntoSegments = (words: Word[], maxSegmentLength: number = 32, maxDuration: number = 3000) => {
  if (!words || words.length === 0) return [];
  
  const segments = [];
  let currentSegment = '';
  let segmentStartTime = words[0].start;
  let segmentEndTime = words[0].end;
  
  for (let i = 0; i < words.length; i++) {
    const word = words[i];
    const wordText = word.text.trim();
    
    // Check if adding this word would exceed limits
    const potentialSegment = currentSegment ? `${currentSegment} ${wordText}` : wordText;
    const wouldExceedLength = potentialSegment.length > maxSegmentLength;
    const wouldExceedDuration = (word.end - segmentStartTime) > maxDuration;
    
    // If we need to start a new segment
    if (currentSegment && (wouldExceedLength || wouldExceedDuration)) {
      segments.push({
        text: currentSegment.trim(),
        start: segmentStartTime,
        end: segmentEndTime
      });
      
      // Start new segment
      currentSegment = wordText;
      segmentStartTime = word.start;
      segmentEndTime = word.end;
    } else {
      // Add word to current segment
      currentSegment = potentialSegment;
      segmentEndTime = word.end;
    }
  }
  
  // Add the last segment
  if (currentSegment) {
    segments.push({
      text: currentSegment.trim(),
      start: segmentStartTime,
      end: segmentEndTime
    });
  }
  
  return segments;
};

/**
 * Generates SRT subtitle content from an array of words
 * @param words - Array of word objects with timing information
 * @returns SRT formatted string
 */
export const generateSRT = (words: Word[]): string => {
  if (!words || words.length === 0) {
    return '';
  }
  
  const segments = groupWordsIntoSegments(words);
  let srtContent = '';
  
  segments.forEach((segment, index) => {
    const segmentIndex = index + 1;
    const startTime = formatTime(segment.start);
    const endTime = formatTime(segment.end);
    
    srtContent += `${segmentIndex}\n`;
    srtContent += `${startTime} --> ${endTime}\n`;
    srtContent += `${segment.text}\n\n`;
  });
  
  return srtContent.trim();
};

/**
 * Creates and downloads an SRT file
 * @param words - Array of word objects with timing information
 * @param filename - Name for the downloaded file (without extension)
 */
export const downloadSRT = (words: Word[], filename: string = 'subtitles') => {
  const srtContent = generateSRT(words);
  
  if (!srtContent) {
    console.warn('No subtitle content to download');
    return;
  }
  
  // Create blob and download
  const blob = new Blob([srtContent], { type: 'text/plain;charset=utf-8' });
  const url = URL.createObjectURL(blob);
  
  // Create temporary download link
  const link = document.createElement('a');
  link.href = url;
  link.download = `${filename}.srt`;
  link.style.display = 'none';
  
  // Trigger download
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Clean up
  URL.revokeObjectURL(url);
};

/**
 * Adjusts word timestamps relative to a clip's start time
 * @param words - Array of word objects
 * @param clipStartMs - Start time of the clip in milliseconds
 * @returns Array of words with adjusted timestamps
 */
export const adjustWordsForClip = (words: Word[], clipStartMs: number): Word[] => {
  return words.map(word => ({
    ...word,
    start: Math.max(0, word.start - clipStartMs),
    end: Math.max(0, word.end - clipStartMs)
  }));
};

/**
 * Generates a timestamp-enriched transcript for AI analysis
 * Groups words into segments and prepends timestamps for better AI understanding
 * @param words - Array of word objects with timing information
 * @returns Formatted string with embedded timestamps
 */
export const generateEnrichedTranscript = (words: Word[]): string => {
  if (!words || words.length === 0) {
    return '';
  }

  const segments = [];
  let currentSegment = [];
  let segmentStartTime = words[0].start;

  for (let i = 0; i < words.length; i++) {
    const word = words[i];
    const prevWord = i > 0 ? words[i - 1] : null;

    // Check if we should start a new segment
    const shouldStartNewSegment =
      currentSegment.length >= 15 || // Max 15 words per segment
      (prevWord && (word.start - prevWord.end) > 700); // Pause > 700ms

    if (shouldStartNewSegment && currentSegment.length > 0) {
      // Finalize current segment
      const segmentText = currentSegment.map(w => w.text).join(' ');
      const timestampSeconds = (segmentStartTime / 1000).toFixed(1);
      segments.push(`[${timestampSeconds}s] ${segmentText}`);

      // Start new segment
      currentSegment = [word];
      segmentStartTime = word.start;
    } else {
      // Add word to current segment
      currentSegment.push(word);
    }
  }

  // Add the final segment
  if (currentSegment.length > 0) {
    const segmentText = currentSegment.map(w => w.text).join(' ');
    const timestampSeconds = (segmentStartTime / 1000).toFixed(1);
    segments.push(`[${timestampSeconds}s] ${segmentText}`);
  }

  return segments.join(' ');
};
