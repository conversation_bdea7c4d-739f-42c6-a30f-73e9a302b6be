import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { generateEnrichedTranscript } from '@/lib/srt';

const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

export async function POST(request: NextRequest) {
  try {
    if (!GEMINI_API_KEY) {
      return NextResponse.json(
        { error: 'Gemini API key not configured' },
        { status: 500 }
      );
    }

    const { transcript } = await request.json();

    if (!transcript) {
      return NextResponse.json(
        { error: 'No transcript provided' },
        { status: 400 }
      );
    }

    // Check if transcript is words array or text
    let enrichedTranscript: string;
    if (Array.isArray(transcript)) {
      // Convert words array to enriched transcript
      enrichedTranscript = generateEnrichedTranscript(transcript);
    } else if (typeof transcript === 'string') {
      // If it's already text, use as is (fallback)
      enrichedTranscript = transcript;
    } else {
      return NextResponse.json(
        { error: 'Invalid transcript format' },
        { status: 400 }
      );
    }

    console.log('📝 Enriched transcript preview:', enrichedTranscript.substring(0, 200) + '...');

    // Initialize Gemini AI
    const genAI = new GoogleGenerativeAI(GEMINI_API_KEY);
    const model = genAI.getGenerativeModel({ model: 'gemini-2.5-flash' });

    // Construct the improved prompt
    const prompt = `You are an expert viral video editor for platforms like TikTok, YouTube Shorts, and Instagram Reels. Your task is to analyze the following transcript and identify 3 to 5 of the most engaging, hook-worthy, and high-impact segments to be turned into short clips.

The transcript is formatted with timestamps like [ss.s] indicating the start time in seconds of the following text.

**RULES:**
1. **Clip Duration:** Each clip you identify should be between 10 and 30 seconds long.
2. **Coherency:** Ensure clips are self-contained and don't start or end abruptly in the middle of a sentence.
3. **Selection Criteria:** Prioritize segments that contain:
   - A strong hook or opening question.
   - A key takeaway, lesson, or "Aha!" moment.
   - A surprising statement or a big reveal.
   - High emotional energy (excitement, humor, passion).
4. **Timestamp Accuracy:** The 'start' and 'end' times you provide MUST be derived from the timestamps present in the text. You will need to estimate the end time based on the text that follows a start timestamp.
5. **Output Format:** Your response MUST be a valid JSON array of objects and NOTHING else. Do not include any explanations, markdown formatting, or any text outside of the JSON array.

**EXAMPLE OUTPUT FORMAT:**
[
  {
    "start": 15.2,
    "end": 45.8,
    "description": "Explains the main surprising outcome, which makes a great hook."
  },
  {
    "start": 88.5,
    "end": 110.1,
    "description": "A funny and relatable anecdote that is highly shareable."
  }
]

**TRANSCRIPT TO ANALYZE:**
---
${enrichedTranscript}
---`;

    // Generate content
    const result = await model.generateContent(prompt);
    const response = result.response;
    const text = response.text();

    // Extract JSON from the response
    let timestamps;
    try {
      // Try to parse the response directly as JSON
      timestamps = JSON.parse(text);
    } catch (parseError) {
      // If direct parsing fails, try to extract JSON from the text
      const jsonMatch = text.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        timestamps = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('Could not extract valid JSON from AI response');
      }
    }

    // Validate the response format
    if (!Array.isArray(timestamps)) {
      throw new Error('AI response is not an array');
    }

    // Validate each timestamp object
    for (const timestamp of timestamps) {
      if (typeof timestamp.start !== 'number' || typeof timestamp.end !== 'number') {
        throw new Error('Invalid timestamp format');
      }
      if (timestamp.start >= timestamp.end) {
        throw new Error('Invalid timestamp range');
      }
      // Validate clip duration (15-60 seconds as per requirements)
      const duration = timestamp.end - timestamp.start;
      if (duration < 15 || duration > 60) {
        console.warn(`Clip duration ${duration}s is outside recommended range (15-60s)`);
      }
    }

    console.log(`✅ Analysis complete: Found ${timestamps.length} clips`);
    timestamps.forEach((clip, index) => {
      console.log(`   Clip ${index + 1}: ${clip.start}s - ${clip.end}s (${(clip.end - clip.start).toFixed(1)}s) - ${clip.description || 'No description'}`);
    });

    return NextResponse.json(timestamps);

  } catch (error) {
    console.error('Analysis error:', error);
    return NextResponse.json(
      { error: 'Failed to analyze transcript' },
      { status: 500 }
    );
  }
}
