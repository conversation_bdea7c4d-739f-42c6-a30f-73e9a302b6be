'use client';

import { useState, useRef, useEffect } from 'react';
import { FFmpeg } from '@ffmpeg/ffmpeg';
import { fetchFile } from '@ffmpeg/util';
import { adjustWordsForClip } from '@/lib/srt';
import Header from '@/components/Header';
import Uploader from '@/components/Uploader';
import ProcessingView from '@/components/ProcessingView';

export default function Home() {
  const [appState, setAppState] = useState<'idle' | 'loaded' | 'processing' | 'error'>('idle');
  const [videoFile, setVideoFile] = useState<File | null>(null);
  const [processingStep, setProcessingStep] = useState<string>('idle');
  const [resultClips, setResultClips] = useState<any[]>([]);
  const [progress, setProgress] = useState<number>(0);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [ffmpegLoaded, setFfmpegLoaded] = useState(false);
  const ffmpegRef = useRef<FFmpeg | null>(null);

  // Load FFmpeg on component mount
  useEffect(() => {
    const loadFfmpeg = async () => {
      try {
        if (!ffmpegRef.current) {
          ffmpegRef.current = new FFmpeg();
        }

        const ffmpeg = ffmpegRef.current;

        // Set up progress handler
        ffmpeg.on('log', ({ message }) => {
          console.log('🔧 FFmpeg log:', message);
        });

        ffmpeg.on('progress', ({ progress, time }) => {
          console.log('📊 FFmpeg progress:', { progress, time });
          setProgress(Math.round(progress * 100));
        });

        // Load the library
        await ffmpeg.load();
        setFfmpegLoaded(true);
        console.log('✅ FFmpeg loaded successfully');
      } catch (error) {
        console.error('❌ Failed to load FFmpeg:', error);
        setErrorMessage('Failed to initialize video processing engine. Please refresh the page.');
      }
    };

    loadFfmpeg();
  }, []);

  const handleFileSelect = (file: File) => {
    setVideoFile(file);
    setAppState('loaded');
  };

  const handleReset = () => {
    setAppState('idle');
    setVideoFile(null);
    setProcessingStep('idle');
    setResultClips([]);
    setProgress(0);
    setErrorMessage(null);
  };

  const debugAudioExtraction = async () => {
    if (!videoFile || !ffmpegRef.current) return;

    try {
      console.log('🐛 DEBUG: Testing audio extraction only...');
      const ffmpeg = ffmpegRef.current;

      // Extract audio
      await ffmpeg.writeFile('input.mp4', await fetchFile(videoFile));
      await ffmpeg.exec(['-i', 'input.mp4', '-vn', '-acodec', 'mp3', '-ab', '128k', '-ar', '16000', 'audio.mp3']);
      const audioData = await ffmpeg.readFile('audio.mp3');
      const audioBlob = new Blob([audioData as any], { type: 'audio/mp3' });

      // Send to debug API
      const formData = new FormData();
      formData.append('audio', audioBlob);

      const response = await fetch('/api/debug-audio', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();
      console.log('🐛 DEBUG: Audio debug result:', result);

      if (result.downloadUrl) {
        console.log('🐛 DEBUG: Audio file available at:', window.location.origin + result.downloadUrl);
        alert(`Audio extracted and saved! Check console for download link or visit: ${result.downloadUrl}`);
      }
    } catch (error) {
      console.error('🐛 DEBUG: Audio extraction failed:', error);
    }
  };

  const handleStartProcessing = async () => {
    if (!videoFile || !ffmpegRef.current) return;

    try {
      setAppState('processing');
      setErrorMessage(null);
      setProgress(0);
      const ffmpeg = ffmpegRef.current;

      // Step 1: Extract Audio
      console.log('🎵 Starting audio extraction...');
      console.log('📁 Original video file:', {
        name: videoFile.name,
        size: videoFile.size,
        type: videoFile.type
      });

      setProcessingStep('extractingAudio');
      await ffmpeg.writeFile('input.mp4', await fetchFile(videoFile));
      console.log('✅ Video file written to FFmpeg filesystem');

      // Extract audio with more detailed logging
      console.log('🔄 Running FFmpeg audio extraction command...');
      await ffmpeg.exec(['-i', 'input.mp4', '-vn', '-acodec', 'mp3', '-ab', '128k', '-ar', '16000', 'audio.mp3']);
      console.log('✅ FFmpeg audio extraction completed');
      setProgress(0); // Reset progress for next step

      const audioData = await ffmpeg.readFile('audio.mp3');
      console.log('📊 Extracted audio data size:', audioData.length, 'bytes');

      // Convert FileData to Blob - FFmpeg returns Uint8Array
      const audioBlob = new Blob([audioData as any], { type: 'audio/mp3' });
      console.log('🎵 Audio blob created:', {
        size: audioBlob.size,
        type: audioBlob.type
      });

      // Save audio file temporarily for debugging
      const audioUrl = URL.createObjectURL(audioBlob);
      console.log('🔗 Audio URL for debugging:', audioUrl);

      // Create a temporary download link for debugging
      const debugLink = document.createElement('a');
      debugLink.href = audioUrl;
      debugLink.download = `debug_audio_${Date.now()}.mp3`;
      debugLink.style.display = 'none';
      document.body.appendChild(debugLink);
      console.log('🐛 Debug: Audio file available for download. Check browser console for link.');
      console.log('🐛 To download audio file, run: document.querySelector("a[download*=debug_audio]").click()');

      // Clean up the debug link after 5 minutes
      setTimeout(() => {
        document.body.removeChild(debugLink);
        URL.revokeObjectURL(audioUrl);
      }, 300000);

      // Step 2: Transcribe
      console.log('📝 Starting transcription...');
      setProcessingStep('transcribing');
      const formData = new FormData();
      formData.append('audio', audioBlob);
      console.log('📤 Sending audio to transcription API...');

      const transcribeResponse = await fetch('/api/transcribe', {
        method: 'POST',
        body: formData,
      });

      console.log('📥 Transcription API response status:', transcribeResponse.status);

      if (!transcribeResponse.ok) {
        const errorText = await transcribeResponse.text();
        console.error('❌ Transcription failed:', errorText);
        throw new Error(`Transcription failed: ${errorText}`);
      }

      const transcriptData = await transcribeResponse.json();
      console.log('✅ Transcription completed:', {
        textLength: transcriptData.text?.length || 0,
        wordsCount: transcriptData.words?.length || 0,
        confidence: transcriptData.confidence,
        audio_duration: transcriptData.audio_duration
      });

      // Step 3: Analyze
      setProcessingStep('analyzing');
      const analyzeResponse = await fetch('/api/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ transcript: transcriptData.words }),
      });

      if (!analyzeResponse.ok) {
        throw new Error('Analysis failed');
      }

      const timestamps = await analyzeResponse.json();

      // Step 4: Clip Videos
      setProcessingStep('clipping');
      const clips = [];

      for (let i = 0; i < timestamps.length; i++) {
        const { start, end } = timestamps[i];
        const outputFileName = `clip_${i}.mp4`;

        // Use FFmpeg to cut the video
        await ffmpeg.exec([
          '-i', 'input.mp4',
          '-ss', start.toString(),
          '-t', (end - start).toString(),
          '-c', 'copy',
          outputFileName
        ]);

        // Read the output file
        const clipData = await ffmpeg.readFile(outputFileName);
        const clipBlob = new Blob([clipData as any], { type: 'video/mp4' });
        const clipUrl = URL.createObjectURL(clipBlob);

        // Find the corresponding transcript words for this clip
        const clipWords = transcriptData.words
          ?.filter((word: any) => word.start >= start * 1000 && word.end <= end * 1000) || [];

        // Adjust word timestamps to be relative to the clip start
        const adjustedWords = adjustWordsForClip(clipWords, start * 1000);

        const transcriptSegment = clipWords
          .map((word: any) => word.text)
          .join(' ') || `Clip ${i + 1}`;

        clips.push({
          url: clipUrl,
          transcript: transcriptSegment,
          words: adjustedWords, // Include adjusted words for subtitle generation
          start,
          end,
          blob: clipBlob
        });

        setProgress(0); // Reset progress for next clip
      }

      // Step 5: Finish
      setResultClips(clips);
      setProcessingStep('done');

    } catch (error) {
      console.error('Processing failed:', error);
      setErrorMessage('An unexpected error occurred. Please check the console or try a different file.');
      setAppState('error');
      setProgress(0);
    }
  };

  return (
    <div className="max-w-4xl p-4 sm:p-8">
      <Header />
      <div className="mt-8">
        {appState === 'idle' ? (
          <Uploader onFileSelect={handleFileSelect} />
        ) : (
          <ProcessingView
            videoFile={videoFile}
            appState={appState}
            processingStep={processingStep}
            resultClips={resultClips}
            onStartProcessing={handleStartProcessing}
            onReset={handleReset}
            ffmpegRef={ffmpegRef}
            onDebugAudio={debugAudioExtraction}
            progress={progress}
            errorMessage={errorMessage}
            ffmpegLoaded={ffmpegLoaded}
          />
        )}
      </div>
    </div>
  );
}
